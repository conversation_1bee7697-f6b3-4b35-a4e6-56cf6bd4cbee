// /**
//  * Admin Dashboard Page
//  *
//  * This page shows the dashboard for Admin users.
//  *
//  * English: This page shows the Admin dashboard with user registration
//  * Tanglish: Indha page Admin-kku dashboard-a display pannum, user registration-oda
//  */

// import { useState, useEffect } from 'react';
// import DashboardLayout from '../components/DashboardLayout';
// import userService from '../services/userService';
// import studentService from '../services/studentService';
// import courseService from '../services/courseService';
// import parentService from '../services/parentService';
// import authService from '../services/authService';

// const AdminDashboard = () => {
//   // State for tabs
//   const [activeTab, setActiveTab] = useState('teachers');

//   // State for users
//   const [users, setUsers] = useState([]);
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState('');

//   // State for user form
//   const [newUser, setNewUser] = useState({
//     username: '',
//     password: '',
//     email: '',
//     role: 'Teacher',
//     course: '',
//     is_admin: false
//   });

//   // State for courses
//   const [courses, setCourses] = useState([]);
//   const [loadingCourses, setLoadingCourses] = useState(false);
//   const [courseError, setCourseError] = useState('');

//   // State for course form
//   const [newCourse, setNewCourse] = useState({
//     name: '',
//     description: ''
//   });

//   /**
//    * Load users from API
//    *
//    * English: This function loads all users from the API
//    * Tanglish: Indha function API-la irundhu ella users-um load pannum
//    */
//   const loadUsers = async () => {
//     try {
//       setLoading(true);
//       setError('');

//       const data = await userService.getUsers();

//       // Filter users based on active tab
//       if (activeTab === 'teachers') {
//         setUsers((data.users || []).filter(user => user.role === 'Teacher'));
//       } else if (activeTab === 'students') {
//         setUsers((data.users || []).filter(user => user.role === 'Student'));
//       } else if (activeTab === 'parents') {
//         setUsers((data.users || []).filter(user => user.role === 'Parent'));
//       }
//     } catch (error) {
//       setError(error.error || 'Failed to load users');
//     } finally {
//       setLoading(false);
//     }
//   };

//   /**
//    * Handle user form input change
//    *
//    * @param {Event} e - Input change event
//    *
//    * English: This function handles changes to the user form inputs
//    * Tanglish: Indha function user form inputs-la changes-a handle pannum
//    */
//   const handleUserInputChange = (e) => {
//     const { name, value, type, checked } = e.target;
//     setNewUser({
//       ...newUser,
//       [name]: type === 'checkbox' ? checked : value
//     });
//   };

//   /**
//    * Handle user form submission
//    *
//    * @param {Event} e - Form submit event
//    *
//    * English: This function handles user form submission to register a new user
//    * Tanglish: Indha function user form submit-a handle panni puthusa oru user-a register pannum
//    */
//   const handleUserSubmit = async (e) => {
//     e.preventDefault();

//     try {
//       setLoading(true);
//       setError('');

//       // Get the admin's main_code from localStorage
//       const adminMainCode = authService.getSchoolCode();

//       if (!adminMainCode) {
//         setError('School code not found. Please contact super admin.');
//         setLoading(false);
//         return;
//       }

//       // Register user with the admin's main_code
//       await studentService.registerStudent({
//         ...newStudent,
//         user_id: userData.user.id,
//         main_code: teacherMainCode
//       });

//       // Reset forms
//       setNewUser({
//         username: '',
//         password: '',
//         email: '',
//         role: 'Student'
//       });

//       setNewStudent({
//         user_id: '',
//         first_name: '',
//         last_name: '',
//         date_of_birth: '',
//         address: '',
//         phone: ''
//       });

//       // Show success message
//       setError(`${newUser.role} registered successfully`);

//       // Reload users
//       await loadUsers();
//     } catch (error) {
//       setError(error.error || `Failed to register ${newUser.role.toLowerCase()}`);
//     } finally {
//       setLoading(false);
//     }
//   };

//   // Set role based on active tab when it changes
//   useEffect(() => {
//     setNewUser(prev => ({
//       ...prev,
//       role: activeTab === 'teachers' ? 'Teacher' : activeTab === 'students' ? 'Student' : 'Parent'
//     }));
//   }, [activeTab]);

//   /**
//    * Load courses from API
//    *
//    * English: This function loads all courses from the API
//    * Tanglish: Indha function API-la irundhu ella courses-um load pannum
//    */
//   const loadCourses = async () => {
//     try {
//       setLoadingCourses(true);
//       setCourseError('');

//       const data = await courseService.getCourses();
//       setCourses(data.courses || []);
//     } catch (error) {
//       setCourseError(error.error || 'Failed to load courses');
//     } finally {
//       setLoadingCourses(false);
//     }
//   };

//   /**
//    * Handle course form input change
//    *
//    * @param {Event} e - Input change event
//    *
//    * English: This function handles changes to the course form inputs
//    * Tanglish: Indha function course form inputs-la changes-a handle pannum
//    */
//   const handleCourseInputChange = (e) => {
//     const { name, value } = e.target;
//     setNewCourse({
//       ...newCourse,
//       [name]: value
//     });
//   };

//   /**
//    * Handle course form submission
//    *
//    * @param {Event} e - Form submit event
//    *
//    * English: This function handles course form submission to create a new course
//    * Tanglish: Indha function course form submit-a handle panni puthusa oru course-a create pannum
//    */
//   const handleCourseSubmit = async (e) => {
//     e.preventDefault();

//     try {
//       setLoadingCourses(true);
//       setCourseError('');

//       // Get the admin's main_code from localStorage
//       const adminMainCode = authService.getSchoolCode();

//       if (!adminMainCode) {
//         setCourseError('School code not found. Please contact super admin.');
//         setLoadingCourses(false);
//         return;
//       }

//       // Create course with the admin's main_code
//       await courseService.createCourse({
//         ...newCourse,
//         main_code: adminMainCode
//       });

//       // Reset form
//       setNewCourse({
//         name: '',
//         description: ''
//       });

//       // Show success message
//       setCourseError('Course created successfully');

//       // Reload courses
//       await loadCourses();
//     } catch (error) {
//       setCourseError(error.error || 'Failed to create course');
//     } finally {
//       setLoadingCourses(false);
//     }
//   };

//   // Load data when component mounts or tab changes
//   useEffect(() => {
//     if (activeTab === 'courses') {
//       loadCourses();
//     } else {
//       loadUsers();
//     }
//   }, [activeTab]);

//   return (
//     <DashboardLayout title="Admin Dashboard">
//       <div className="mb-6">
//         <div className="flex flex-wrap border-b bg-gradient-to-r from-blue-100 to-purple-100 rounded-t-lg shadow-md">
//           <button
//             className={`py-3 px-5 font-semibold transition-all duration-300 ${activeTab === 'teachers' ? 'border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner' : 'text-gray-600 hover:text-indigo-500'}`}
//             onClick={() => setActiveTab('teachers')}
//           >
//             Teacher Registration
//           </button>
//           <button
//             className={`py-3 px-5 font-semibold transition-all duration-300 ${activeTab === 'students' ? 'border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner' : 'text-gray-600 hover:text-indigo-500'}`}
//             onClick={() => setActiveTab('students')}
//           >
//             Student Registration
//           </button>
//           <button
//             className={`py-3 px-5 font-semibold transition-all duration-300 ${activeTab === 'parents' ? 'border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner' : 'text-gray-600 hover:text-indigo-500'}`}
//             onClick={() => setActiveTab('parents')}
//           >
//             Parent Registration
//           </button>
//           <button
//             className={`py-3 px-5 font-semibold transition-all duration-300 ${activeTab === 'courses' ? 'border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner' : 'text-gray-600 hover:text-indigo-500'}`}
//             onClick={() => setActiveTab('courses')}
//           >
//             Course Management
//           </button>
//         </div>
//       </div>

//       <div className={`card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 ${
//         activeTab === 'teachers' ? 'border-green-500' :
//         activeTab === 'students' ? 'border-purple-500' :
//         activeTab === 'parents' ? 'border-teal-500' :
//         'border-indigo-500'
//       }`}>
//         <h2 className={`text-2xl font-bold mb-4 ${
//           activeTab === 'teachers' ? 'text-green-700' :
//           activeTab === 'students' ? 'text-purple-700' :
//           activeTab === 'parents' ? 'text-teal-700' :
//           'text-indigo-700'
//         }`}>
//           Register New {activeTab === 'teachers' ? 'Teacher' : activeTab === 'students' ? 'Student' : activeTab === 'parents' ? 'Parent' : 'Course'}
//         </h2>

//         {error && (
//           <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
//             {error}
//           </div>
//         )}

//         <form onSubmit={handleUserSubmit}>
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             <div className="form-group">
//               <label htmlFor="username" className="form-label text-gray-700 font-medium">Username</label>
//               <input
//                 type="text"
//                 className={`form-input w-full rounded-md border-gray-300 shadow-sm focus:ring focus:ring-opacity-50 ${
//                   activeTab === 'teachers' ? 'focus:border-green-500 focus:ring-green-200' :
//                   activeTab === 'students' ? 'focus:border-purple-500 focus:ring-purple-200' :
//                   'focus:border-teal-500 focus:ring-teal-200'
//                 }`}
//                 id="username"
//                 name="username"
//                 value={newUser.username}
//                 onChange={handleUserInputChange}
//                 required
//               />
//             </div>

//             <div className="form-group">
//               <label htmlFor="password" className="form-label text-gray-700 font-medium">Password</label>
//               <input
//                 type="password"
//                 className={`form-input w-full rounded-md border-gray-300 shadow-sm focus:ring focus:ring-opacity-50 ${
//                   activeTab === 'teachers' ? 'focus:border-green-500 focus:ring-green-200' :
//                   activeTab === 'students' ? 'focus:border-purple-500 focus:ring-purple-200' :
//                   'focus:border-teal-500 focus:ring-teal-200'
//                 }`}
//                 id="password"
//                 name="password"
//                 value={newUser.password}
//                 onChange={handleUserInputChange}
//                 required
//               />
//             </div>

//             <div className="form-group">
//               <label htmlFor="email" className="form-label text-gray-700 font-medium">Email</label>
//               <input
//                 type="email"
//                 className={`form-input w-full rounded-md border-gray-300 shadow-sm focus:ring focus:ring-opacity-50 ${
//                   activeTab === 'teachers' ? 'focus:border-green-500 focus:ring-green-200' :
//                   activeTab === 'students' ? 'focus:border-purple-500 focus:ring-purple-200' :
//                   'focus:border-teal-500 focus:ring-teal-200'
//                 }`}
//                 id="email"
//                 name="email"
//                 value={newUser.email}
//                 onChange={handleUserInputChange}
//                 required
//               />
//             </div>

//             {activeTab === 'teachers' && (
//               <>
//                 <div className="form-group">
//                   <label className="inline-flex items-center mt-4">
//                     <input
//                       type="checkbox"
//                       className="form-checkbox rounded text-green-600 focus:ring-green-500"
//                       id="is_admin"
//                       name="is_admin"
//                       checked={newUser.is_admin}
//                       onChange={handleUserInputChange}
//                     />
//                     <span className="ml-2 text-gray-700">Is Admin</span>
//                   </label>
//                 </div>
//                 <div className="form-group">
//                   <label htmlFor="course" className="form-label text-gray-700 font-medium">Course</label>
//                   <select
//                     className="form-select w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50"
//                     id="course"
//                     name="course"
//                     value={newUser.course}
//                     onChange={handleUserInputChange}
//                   >
//                     <option value="">Select Course</option>
//                     <option value="Neet">Neet</option>
//                     <option value="Jee">Jee</option>
//                   </select>
//                 </div>
//               </>
//             )}
//           </div>

//           <button
//             type="submit"
//             className={`mt-4 px-6 py-2 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-colors duration-300 ${
//               activeTab === 'teachers' ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500' :
//               activeTab === 'students' ? 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500' :
//               'bg-teal-600 hover:bg-teal-700 focus:ring-teal-500'
//             }`}
//             disabled={loading}
//           >
//             {loading ? 'Registering...' : `Register ${activeTab === 'teachers' ? 'Teacher' : activeTab === 'students' ? 'Student' : 'Parent'}`}
//           </button>
//         </form>
//       </div>

//       <div className="card bg-white rounded-lg shadow-lg p-6">
//         <h2 className={`text-2xl font-bold mb-4 ${
//           activeTab === 'teachers' ? 'text-green-700' :
//           activeTab === 'students' ? 'text-purple-700' :
//           activeTab === 'parents' ? 'text-teal-700' :
//           'text-indigo-700'
//         }`}>
//           All {activeTab === 'teachers' ? 'Teachers' : activeTab === 'students' ? 'Students' : activeTab === 'parents' ? 'Parents' : 'Courses'}
//         </h2>

//         {loading ? (
//           <p className="text-gray-600">Loading {activeTab}...</p>
//         ) : (
//           <div className="overflow-x-auto">
//             <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
//               <thead className="bg-gray-50">
//                 <tr>
//                   <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
//                   <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
//                   <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
//                   {activeTab === 'teachers' && (
//                     <>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Is Admin</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
//                     </>
//                   )}
//                   <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Main Code</th>
//                 </tr>
//               </thead>
//               <tbody className="divide-y divide-gray-200">
//                 {users.map((user) => (
//                   <tr key={user.id} className="hover:bg-gray-50">
//                     <td className="py-3 px-4 text-sm text-gray-900">{user.id}</td>
//                     <td className="py-3 px-4 text-sm text-gray-900 font-medium">{user.username}</td>
//                     <td className="py-3 px-4 text-sm text-gray-500">{user.email}</td>
//                     {activeTab === 'teachers' && (
//                       <>
//                         <td className="py-3 px-4 text-sm text-gray-500">
//                           {user.is_admin ?
//                             <span className="px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium">Yes</span> :
//                             <span className="px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs font-medium">No</span>
//                           }
//                         </td>
//                         <td className="py-3 px-4 text-sm text-gray-500">
//                           {user.course ?
//                             <span className="px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">{user.course}</span> :
//                             <span className="px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs font-medium">None</span>
//                           }
//                         </td>
//                       </>
//                     )}
//                     <td className="py-3 px-4 text-sm text-gray-500">{user.main_code || '-'}</td>
//                   </tr>
//                 ))}
//               </tbody>
//             </table>
//           </div>
//         )}
//       </div>

//       {activeTab === 'courses' && (
//         <div>
//           <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-indigo-500">
//             <h2 className="text-2xl font-bold mb-4 text-indigo-700">Create New Course</h2>

//             {courseError && (
//               <div className={`p-4 mb-4 rounded-md ${courseError.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
//                 {courseError}
//               </div>
//             )}

//             <form onSubmit={handleCourseSubmit}>
//               <div className="grid grid-cols-1 gap-4">
//                 <div className="form-group">
//                   <label htmlFor="name" className="form-label text-gray-700 font-medium">Course Name</label>
//                   <input
//                     type="text"
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
//                     id="name"
//                     name="name"
//                     value={newCourse.name}
//                     onChange={handleCourseInputChange}
//                     required
//                   />
//                 </div>

//                 <div className="form-group">
//                   <label htmlFor="description" className="form-label text-gray-700 font-medium">Description</label>
//                   <textarea
//                     className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
//                     id="description"
//                     name="description"
//                     value={newCourse.description}
//                     onChange={handleCourseInputChange}
//                     rows="3"
//                   ></textarea>
//                 </div>
//               </div>

//               <button type="submit" className="mt-4 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loadingCourses}>
//                 {loadingCourses ? 'Creating...' : 'Create Course'}
//               </button>
//             </form>
//           </div>

//           <div className="card bg-white rounded-lg shadow-lg p-6">
//             <h2 className="text-2xl font-bold mb-4 text-indigo-700">All Courses</h2>

//             {loadingCourses ? (
//               <p className="text-gray-600">Loading courses...</p>
//             ) : (
//               <div className="overflow-x-auto">
//                 <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
//                   <thead className="bg-gray-50">
//                     <tr>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
//                       <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Main Code</th>
//                     </tr>
//                   </thead>
//                   <tbody className="divide-y divide-gray-200">
//                     {courses.map((course) => (
//                       <tr key={course.id} className="hover:bg-gray-50">
//                         <td className="py-3 px-4 text-sm text-gray-900">{course.id}</td>
//                         <td className="py-3 px-4 text-sm text-gray-900 font-medium">{course.name}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{course.description}</td>
//                         <td className="py-3 px-4 text-sm text-gray-500">{course.main_code || '-'}</td>
//                       </tr>
//                     ))}
//                   </tbody>
//                 </table>
//               </div>
//             )}
//           </div>
//         </div>
//       )}
//     </DashboardLayout>
//   );
// };

// export default AdminDashboard;





/**
 * Admin Dashboard Page
 *
 * This page shows the dashboard for Admin users.
 *
 * English: This page shows the Admin dashboard with user registration, course management, and mapping
 * Tanglish: Indha page Admin-kku dashboard-a display pannum, user registration, course management, mapping-oda
 */

import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import DashboardLayout from '../dashboard/DashboardLayout';
import userService from '../../services/userService';
import studentService from '../../services/studentService';
import courseService from '../../services/courseService';
import parentService from '../../services/parentService';
import authService from '../../services/authService';

const AdminDashboard = () => {
  const location = useLocation();

  // Get active section from URL parameters
  const searchParams = new URLSearchParams(location.search);
  const activeSection = searchParams.get('tab') || 'dashboard';

  // State for users
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // State for students
  const [students, setStudents] = useState([]);
  const [loadingStudents, setLoadingStudents] = useState(false);

  // State for courses
  const [courses, setCourses] = useState([]);
  const [loadingCourses, setLoadingCourses] = useState(false);

  // State for parents
  const [parents, setParents] = useState([]);
  const [loadingParents, setLoadingParents] = useState(false);

  // State for user form (for teacher/student/parent registration)
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    email: '',
    role: 'Teacher',
    course: '',
    is_admin: false
  });

  // State for student form
  const [newStudent, setNewStudent] = useState({
    user_id: '',
    first_name: '',
    last_name: '',
    date_of_birth: '',
    address: '',
    phone: ''
  });

  // State for parent form
  const [newParent, setNewParent] = useState({
    user_id: '',
    first_name: '',
    last_name: '',
    occupation: '',
    address: '',
    phone: ''
  });

  // State for course form
  const [newCourse, setNewCourse] = useState({
    name: '',
    description: ''
  });

  // State for course mapping form
  const [courseMapping, setCourseMapping] = useState({
    student_id: '',
    course_id: ''
  });

  // State for parent mapping form
  const [parentMapping, setParentMapping] = useState({
    parent_id: '',
    student_id: '',
    relationship: ''
  });

  // State for existing parent mappings
  const [existingParents, setExistingParents] = useState([]);

  /**
   * Load users from API
   */
  const loadUsers = async () => {
    try {
      setLoading(true);
      setError('');

      const data = await userService.getUsers();
      if (activeTab === 'teachers') {
        setUsers((data.users || []).filter(user => user.role === 'Teacher'));
      }
    } catch (error) {
      setError(error.error || 'Failed to load teachers');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load students from API
   */
  const loadStudents = async () => {
    try {
      setLoadingStudents(true);
      setError('');

      const data = await studentService.getStudents();
      setStudents(data.students || []);
    } catch (error) {
      setError(error.error || 'Failed to load students');
    } finally {
      setLoadingStudents(false);
    }
  };

  /**
   * Load courses from API
   */
  const loadCourses = async () => {
    try {
      setLoadingCourses(true);
      setError('');

      const data = await courseService.getCourses();
      setCourses(data.courses || []);
    } catch (error) {
      setError(error.error || 'Failed to load courses');
    } finally {
      setLoadingCourses(false);
    }
  };

  /**
   * Load parents from API
   */
  const loadParents = async () => {
    try {
      setLoadingParents(true);
      setError('');

      const data = await parentService.getParents();
      setParents(data.parents || []);
    } catch (error) {
      setError(error.error || 'Failed to load parents');
    } finally {
      setLoadingParents(false);
    }
  };

  /**
   * Handle user form input change
   */
  const handleUserInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewUser({
      ...newUser,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  /**
   * Handle student form input change
   */
  const handleStudentInputChange = (e) => {
    const { name, value } = e.target;
    setNewStudent({
      ...newStudent,
      [name]: value
    });
  };

  /**
   * Handle parent form input change
   */
  const handleParentInputChange = (e) => {
    const { name, value } = e.target;
    setNewParent({
      ...newParent,
      [name]: value
    });
  };

  /**
   * Handle course form input change
   */
  const handleCourseInputChange = (e) => {
    const { name, value } = e.target;
    setNewCourse({
      ...newCourse,
      [name]: value
    });
  };

  /**
   * Handle course mapping form input change
   */
  const handleCourseMappingChange = (e) => {
    const { name, value } = e.target;
    setCourseMapping({
      ...courseMapping,
      [name]: value
    });
  };

  /**
   * Handle parent mapping form input change
   */
  const handleParentMappingChange = async (e) => {
    const { name, value } = e.target;
    setParentMapping({
      ...parentMapping,
      [name]: value
    });

    // If student_id is selected, fetch existing parents for this student
    if (name === 'student_id' && value) {
      try {
        setLoading(true);
        const data = await studentService.getStudentParents(value);
        setExistingParents(data.parents || []);

        // If student already has parents, show a message
        if (data.parents && data.parents.length > 0) {
          const parentNames = data.parents.map(p => `${p.first_name} ${p.last_name} (${p.relationship})`).join(', ');
          setError(`Note: This student already has the following parents mapped: ${parentNames}`);
        } else {
          setError('');
        }
      } catch (error) {
        console.error('Error fetching student parents:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  /**
   * Handle teacher registration form submission
   */
  const handleTeacherRegistration = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      const adminMainCode = authService.getSchoolCode();
      if (!adminMainCode) {
        setError('School code not found. Please contact super admin.');
        setLoading(false);
        return;
      }

      await userService.registerUser({
        ...newUser,
        role: 'Teacher',
        main_code: adminMainCode
      });

      setNewUser({
        username: '',
        password: '',
        email: '',
        role: 'Teacher',
        course: '',
        is_admin: false
      });

      setError('Teacher registered successfully');
      await loadUsers();
    } catch (error) {
      setError(error.error || 'Failed to register teacher');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle student registration form submission
   */
  const handleStudentRegistration = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      const adminMainCode = authService.getSchoolCode();
      if (!adminMainCode) {
        setError('School code not found. Please contact super admin.');
        setLoading(false);
        return;
      }

      const userData = await userService.registerUser({
        ...newUser,
        role: 'Student',
        main_code: adminMainCode
      });

      await studentService.registerStudent({
        ...newStudent,
        user_id: userData.user.id,
        main_code: adminMainCode
      });

      setNewUser({
        username: '',
        password: '',
        email: '',
        role: 'Student'
      });

      setNewStudent({
        user_id: '',
        first_name: '',
        last_name: '',
        date_of_birth: '',
        address: '',
        phone: ''
      });

      setError('Student registered successfully');
      await loadStudents();
    } catch (error) {
      setError(error.error || 'Failed to register student');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle parent registration form submission
   */
  const handleParentRegistration = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      const adminMainCode = authService.getSchoolCode();
      if (!adminMainCode) {
        setError('School code not found. Please contact super admin.');
        setLoading(false);
        return;
      }

      const userData = await userService.registerUser({
        ...newUser,
        role: 'Parent',
        main_code: adminMainCode
      });

      await parentService.registerParent({
        ...newParent,
        user_id: userData.user.id,
        main_code: adminMainCode
      });

      setNewUser({
        username: '',
        password: '',
        email: '',
        role: 'Parent'
      });

      setNewParent({
        user_id: '',
        first_name: '',
        last_name: '',
        occupation: '',
        address: '',
        phone: ''
      });

      setError('Parent registered successfully');
      await loadParents();
    } catch (error) {
      setError(error.error || 'Failed to register parent');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle course creation form submission
   */
  const handleCourseCreation = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      const adminMainCode = authService.getSchoolCode();
      if (!adminMainCode) {
        setError('School code not found. Please contact super admin.');
        setLoading(false);
        return;
      }

      await courseService.createCourse({
        ...newCourse,
        main_code: adminMainCode
      });

      setNewCourse({
        name: '',
        description: ''
      });

      setError('Course created successfully');
      await loadCourses();
    } catch (error) {
      setError(error.error || 'Failed to create course');
    } finally {
      setLoading(false);
    }
  };

  const handleCourseMapping = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      await courseService.mapStudentToCourse(
        courseMapping.student_id,
        courseMapping.course_id
      );

      setCourseMapping({
        student_id: '',
        course_id: ''
      });

      setError('Student mapped to course successfully');
    } catch (error) {
      setError(error.error || 'Failed to map student to course');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle parent mapping form submission
   */
  const handleParentMapping = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      await studentService.mapParentToStudent(
        parentMapping.parent_id,
        parentMapping.student_id,
        parentMapping.relationship
      );

      // Reset form and existing parents
      setParentMapping({
        parent_id: '',
        student_id: '',
        relationship: ''
      });
      setExistingParents([]);

      setError('Parent mapped to student successfully');
    } catch (error) {
      setError(error.error || 'Failed to map parent to student');
    } finally {
      setLoading(false);
    }
  };

  // Load data when component mounts or section changes
  useEffect(() => {
    if (activeSection === 'dashboard') {
      // Load all data for dashboard overview
      loadUsers();
      loadStudents();
      loadCourses();
      loadParents();
    } else if (activeSection === 'teachers') {
      loadUsers();
    } else if (activeSection === 'students') {
      loadStudents();
    } else if (activeSection === 'courses') {
      loadCourses();
      loadStudents();
    } else if (activeSection === 'parents') {
      loadParents();
      loadStudents();
    }
  }, [activeSection]);

  // Render dashboard overview
  const renderDashboard = () => (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Students</h3>
          <p className="text-3xl font-bold text-purple-600">{students.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Courses</h3>
          <p className="text-3xl font-bold text-green-600">{courses.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Total Parents</h3>
          <p className="text-3xl font-bold text-teal-600">{parents.length}</p>
        </div>
      </div>
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Welcome to Admin Dashboard</h3>
        <p className="text-gray-600">Use the sidebar navigation to manage teachers, students, courses, and parents.</p>
      </div>
    </div>
  );

  return (
    <DashboardLayout title="Admin Dashboard">
      <div className="container mx-auto px-4 py-8">
        {activeSection === 'dashboard' && renderDashboard()}

        {activeSection === 'teachers' && (
          <div className="space-y-8">
            <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-green-500">
              <h2 className="text-2xl font-bold mb-4 text-green-700">Register New Teacher</h2>

            {error && (
              <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
                {error}
              </div>
            )}

            <form onSubmit={handleTeacherRegistration}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-group">
                  <label htmlFor="username" className="form-label text-gray-700 font-medium">Username</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50"
                    id="username"
                    name="username"
                    value={newUser.username}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="password" className="form-label text-gray-700 font-medium">Password</label>
                  <input
                    type="password"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50"
                    id="password"
                    name="password"
                    value={newUser.password}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email" className="form-label text-gray-700 font-medium">Email</label>
                  <input
                    type="email"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50"
                    id="email"
                    name="email"
                    value={newUser.email}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="course" className="form-label text-gray-700 font-medium">Course</label>
                  <select
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50"
                    id="course"
                    name="course"
                    value={newUser.course}
                    onChange={handleUserInputChange}
                  >
                    <option value="">Select Course</option>
                    <option value="Neet">Neet</option>
                    <option value="Jee">Jee</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="inline-flex items-center mt-4">
                    <input
                      type="checkbox"
                      className="form-checkbox rounded text-green-600 focus:ring-green-500"
                      id="is_admin"
                      name="is_admin"
                      checked={newUser.is_admin}
                      onChange={handleUserInputChange}
                    />
                    <span className="ml-2 text-gray-700">Is Admin</span>
                  </label>
                </div>
              </div>

              <button type="submit" className="mt-4 px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loading}>
                {loading ? 'Registering...' : 'Register Teacher'}
              </button>
            </form>
          </div>

          <div className="card bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-green-700">All Teachers</h2>

            {loading ? (
              <p className="text-gray-600">Loading teachers...</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Is Admin</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Main Code</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {users.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="py-3 px-4 text-sm text-gray-900">{user.id}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 font-medium">{user.username}</td>
                        <td className="py-3 px-4 text-sm text-gray-500">{user.email}</td>
                        <td className="py-3 px-4 text-sm text-gray-500">
                          {user.is_admin ?
                            <span className="px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium">Yes</span> :
                            <span className="px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs font-medium">No</span>
                          }
                        </td>
                        <td className="py-3 px-4 text-sm text-gray-500">
                          {user.course ?
                            <span className="px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">{user.course}</span> :
                            <span className="px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs font-medium">None</span>
                          }
                        </td>
                        <td className="py-3 px-4 text-sm text-gray-500">{user.main_code || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          </div>
        )}

        {activeSection === 'students' && (
          <div className="space-y-8">
          <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500">
            <h2 className="text-2xl font-bold mb-4 text-purple-700">Register New Student</h2>

            {error && (
              <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
                {error}
              </div>
            )}

            <form onSubmit={handleStudentRegistration}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-group">
                  <label htmlFor="username" className="form-label text-gray-700 font-medium">Username</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                    id="username"
                    name="username"
                    value={newUser.username}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="password" className="form-label text-gray-700 font-medium">Password</label>
                  <input
                    type="password"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                    id="password"
                    name="password"
                    value={newUser.password}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email" className="form-label text-gray-700 font-medium">Email</label>
                  <input
                    type="email"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                    id="email"
                    name="email"
                    value={newUser.email}
                    onChange={handleUserInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="first_name" className="form-label text-gray-700 font-medium">First Name</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                    id="first_name"
                    name="first_name"
                    value={newStudent.first_name}
                    onChange={handleStudentInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="last_name" className="form-label text-gray-700 font-medium">Last Name</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                    id="last_name"
                    name="last_name"
                    value={newStudent.last_name}
                    onChange={handleStudentInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="date_of_birth" className="form-label text-gray-700 font-medium">Date of Birth</label>
                  <input
                    type="date"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                    id="date_of_birth"
                    name="date_of_birth"
                    value={newStudent.date_of_birth}
                    onChange={handleStudentInputChange}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="address" className="form-label text-gray-700 font-medium">Address</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                    id="address"
                    name="address"
                    value={newStudent.address}
                    onChange={handleStudentInputChange}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="phone" className="form-label text-gray-700 font-medium">Phone</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                    id="phone"
                    name="phone"
                    value={newStudent.phone}
                    onChange={handleStudentInputChange}
                  />
                </div>
              </div>

              <button type="submit" className="mt-4 px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loading}>
                {loading ? 'Registering...' : 'Register Student'}
              </button>
            </form>
          </div>

          <div className="card bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-purple-700">All Students</h2>

            {loadingStudents ? (
              <p className="text-gray-600">Loading students...</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User ID</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">First Name</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Name</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date of Birth</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {students.map((student) => (
                      <tr key={student.id} className="hover:bg-gray-50">
                        <td className="py-3 px-4 text-sm text-gray-900">{student.id}</td>
                        <td className="py-3 px-4 text-sm text-gray-900">{student.user_id}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 font-medium">{student.first_name}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 font-medium">{student.last_name}</td>
                        <td className="py-3 px-4 text-sm text-gray-500">{student.date_of_birth}</td>
                        <td className="py-3 px-4 text-sm text-gray-500">{student.phone}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          </div>
        )}

        {activeSection === 'courses' && (
          <div className="space-y-8">
          <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-indigo-500">
            <h2 className="text-2xl font-bold mb-4 text-indigo-700">Create New Course</h2>

            {error && (
              <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
                {error}
              </div>
            )}

            <form onSubmit={handleCourseCreation}>
              <div className="grid grid-cols-1 gap-4">
                <div className="form-group">
                  <label htmlFor="name" className="form-label text-gray-700 font-medium">Course Name</label>
                  <input
                    type="text"
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                    id="name"
                    name="name"
                    value={newCourse.name}
                    onChange={handleCourseInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="description" className="form-label text-gray-700 font-medium">Description</label>
                  <textarea
                    className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                    id="description"
                    name="description"
                    value={newCourse.description}
                    onChange={handleCourseInputChange}
                    rows="3"
                  ></textarea>
                </div>
              </div>

              <button type="submit" className="mt-4 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loading}>
                {loading ? 'Creating...' : 'Create Course'}
              </button>
            </form>
          </div>

          <div className="card bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-indigo-700">All Courses</h2>

            {loadingCourses ? (
              <p className="text-gray-600">Loading courses...</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {courses.map((course) => (
                      <tr key={course.id} className="hover:bg-gray-50">
                        <td className="py-3 px-4 text-sm text-gray-900">{course.id}</td>
                        <td className="py-3 px-4 text-sm text-gray-900 font-medium">{course.name}</td>
                        <td className="py-3 px-4 text-sm text-gray-500">{course.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          </div>
        )}

        {activeSection === 'parents' && (
          <div className="space-y-8">
            <div className="card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500">
              <h2 className="text-2xl font-bold mb-4 text-purple-700">Map Student to Course</h2>

              {error && (
                <div className={`p-4 mb-4 rounded-md ${error.includes('successfully') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
                  {error}
                </div>
              )}

              <form onSubmit={handleCourseMapping}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="form-group">
                    <label htmlFor="student_id" className="form-label text-gray-700 font-medium">Student</label>
                    <select
                      className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                      id="student_id"
                      name="student_id"
                      value={courseMapping.student_id}
                      onChange={handleCourseMappingChange}
                      required
                    >
                      <option value="">Select Student</option>
                      {students.map((student) => (
                        <option key={student.id} value={student.id}>
                          {student.first_name} {student.last_name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-group">
                    <label htmlFor="course_id" className="form-label text-gray-700 font-medium">Course</label>
                    <select
                      className="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                      id="course_id"
                      name="course_id"
                      value={courseMapping.course_id}
                      onChange={handleCourseMappingChange}
                      required
                    >
                      <option value="">Select Course</option>
                      {courses.map((course) => (
                        <option key={course.id} value={course.id}>
                          {course.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <button type="submit" className="mt-4 px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-colors duration-300" disabled={loading}>
                  {loading ? 'Mapping...' : 'Map Student to Course'}
                </button>
              </form>
            </div>

            <div className="card bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-bold mb-4 text-purple-700">All Courses</h2>

              {loadingCourses ? (
                <p className="text-gray-600">Loading courses...</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th className="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {courses.map((course) => (
                        <tr key={course.id} className="hover:bg-gray-50">
                          <td className="py-3 px-4 text-sm text-gray-900">{course.id}</td>
                          <td className="py-3 px-4 text-sm text-gray-900 font-medium">{course.name}</td>
                          <td className="py-3 px-4 text-sm text-gray-500">{course.description}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default AdminDashboard;