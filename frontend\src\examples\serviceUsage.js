/**
 * Service Usage Examples
 *
 * This file demonstrates how to use the new all.js service
 * and the updated vite configuration.
 *
 * English: This file shows examples of using the API services
 * Tanglish: Indha file API services-a epdi use pannanum nu example-a kaatuthu
 */

import { allServices, authService } from '../services/all';

// Example 1: Using individual service methods
export const loginExample = async () => {
  try {
    const loginData = {
      username: '<EMAIL>',
      password: 'password123'
    };
    
    const response = await allServices.auth.userLoginService(loginData);
    console.log('Login successful:', response.data);
    return response.data;
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
};

// Example 2: Using student services
export const getStudentsExample = async () => {
  try {
    const response = await allServices.student.getAllStudents();
    console.log('Students fetched:', response.data);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch students:', error);
    throw error;
  }
};

// Example 3: Using course services
export const createCourseExample = async () => {
  try {
    const courseData = {
      name: 'Mathematics',
      description: 'Basic mathematics course',
      duration: '6 months'
    };
    
    const response = await allServices.course.createCourse(courseData);
    console.log('Course created:', response.data);
    return response.data;
  } catch (error) {
    console.error('Failed to create course:', error);
    throw error;
  }
};

// Example 4: Using payment services
export const processPaymentExample = async () => {
  try {
    const paymentData = {
      amount: 1000,
      currency: 'INR',
      studentId: 'student123',
      courseId: 'course456'
    };
    
    const response = await allServices.payment.processPayment(paymentData);
    console.log('Payment processed:', response.data);
    return response.data;
  } catch (error) {
    console.error('Payment failed:', error);
    throw error;
  }
};

// Example 5: Using monitor services
export const checkSystemStatusExample = async () => {
  try {
    const response = await allServices.monitor.getSystemStatus();
    console.log('System status:', response.data);
    return response.data;
  } catch (error) {
    console.error('Failed to get system status:', error);
    throw error;
  }
};

// Example 6: Using evaluation services
export const getEvaluationsExample = async () => {
  try {
    const response = await allServices.evaluation.getEvaluations();
    console.log('Evaluations fetched:', response.data);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch evaluations:', error);
    throw error;
  }
};

// Example 7: Using direct import (alternative way)
export const directImportExample = async () => {
  try {
    const loginData = {
      username: '<EMAIL>',
      password: 'password123'
    };
    
    // Using direct import instead of allServices
    const response = await authService.userLoginService(loginData);
    console.log('Direct import login successful:', response.data);
    return response.data;
  } catch (error) {
    console.error('Direct import login failed:', error);
    throw error;
  }
};

// Example 8: Chaining multiple service calls
export const chainedServicesExample = async () => {
  try {
    // First login
    const loginData = {
      username: '<EMAIL>',
      password: 'password123'
    };
    
    const loginResponse = await allServices.auth.userLoginService(loginData);
    console.log('Login successful:', loginResponse.data);
    
    // Then fetch students
    const studentsResponse = await allServices.student.getAllStudents();
    console.log('Students fetched:', studentsResponse.data);
    
    // Then check system status
    const statusResponse = await allServices.monitor.getSystemStatus();
    console.log('System status:', statusResponse.data);
    
    return {
      login: loginResponse.data,
      students: studentsResponse.data,
      status: statusResponse.data
    };
  } catch (error) {
    console.error('Chained services failed:', error);
    throw error;
  }
};

// Export all examples
export default {
  loginExample,
  getStudentsExample,
  createCourseExample,
  processPaymentExample,
  checkSystemStatusExample,
  getEvaluationsExample,
  directImportExample,
  chainedServicesExample,
};
