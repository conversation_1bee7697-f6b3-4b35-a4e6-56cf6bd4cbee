/**
 * Parent Service
 * 
 * This service handles parent management with the Parent Service.
 * 
 * English: This file handles parent management
 * Tanglish: Indha file parent management-a handle pannum
 */

import axios from 'axios';
import authService from './authService';

const API_URL = 'http://localhost:5004/api/parents';

const parentService = {
  /**
   * Register a new parent
   * 
   * @param {Object} parentData - Parent data (user_id, first_name, last_name, etc.)
   * @returns {Promise} - Promise with parent registration response
   * 
   * English: This function registers a new parent
   * Tanglish: Indha function puthusa oru parent-a register pannum
   */
  registerParent: async (parentData) => {
    try {
      const response = await axios.post(`${API_URL}/parents`, parentData, {
        headers: authService.getAuthHeader()
      });
      
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },
  
  /**
   * Get all parents
   * 
   * @returns {Promise} - Promise with parents response
   * 
   * English: This function gets all parents
   * Tanglish: Indha function ella parents-um get pannum
   */
  getParents: async () => {
    try {
      const response = await axios.get(`${API_URL}/parents`, {
        headers: authService.getAuthHeader()
      });
      
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },
  
  /**
   * Get a specific parent
   * 
   * @param {number} parentId - ID of the parent to get
   * @returns {Promise} - Promise with parent response
   * 
   * English: This function gets a specific parent
   * Tanglish: Indha function specific parent-a get pannum
   */
  getParent: async (parentId) => {
    try {
      const response = await axios.get(`${API_URL}/parents/${parentId}`, {
        headers: authService.getAuthHeader()
      });
      
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },
  
  /**
   * Get a parent by user ID
   * 
   * @param {number} userId - ID of the user
   * @returns {Promise} - Promise with parent response
   * 
   * English: This function gets a parent by user ID
   * Tanglish: Indha function user ID moolama parent-a get pannum
   */
  getParentByUserId: async (userId) => {
    try {
      const response = await axios.get(`${API_URL}/parents/user/${userId}`, {
        headers: authService.getAuthHeader()
      });
      
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  }
};

export default parentService;
