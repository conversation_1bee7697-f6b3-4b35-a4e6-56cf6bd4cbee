/**
 * Course Service
 *
 * This service handles course management with the Course Service.
 *
 * English: This file handles course management
 * Tanglish: Indha file course management-a handle pannum
 */

import axios from 'axios';
import authService from './authService';

const API_URL = 'http://localhost:5003/api/courses';

const courseService = {
  /**
   * Create a new course
   *
   * @param {Object} courseData - Course data (name, description)
   * @returns {Promise} - Promise with course creation response
   *
   * English: This function creates a new course
   * Tanglish: Indha function puthusa oru course-a create pannum
   */
  createCourse: async (courseData) => {
    try {
      const response = await axios.post(`${API_URL}/courses`, courseData, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Get all courses
   *
   * @returns {Promise} - Promise with courses response
   *
   * English: This function gets all courses
   * Tanglish: Indha function ella courses-um get pannum
   */
  getCourses: async () => {
    try {
      const response = await axios.get(`${API_URL}/courses`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });

      return response.data;
    } catch (error) {
      // Check if it's a CORS error or other network error
      if (!error.response) {
        console.warn("CORS or network error when fetching courses");
        // Return empty courses array instead of throwing error
        return { courses: [] };
      }
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Get a specific course
   *
   * @param {number} courseId - ID of the course to get
   * @returns {Promise} - Promise with course response
   *
   * English: This function gets a specific course
   * Tanglish: Indha function specific course-a get pannum
   */
  getCourse: async (courseId) => {
    try {
      const response = await axios.get(`${API_URL}/courses/${courseId}`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Map a student to a course
   *
   * @param {number} studentId - ID of the student
   * @param {number} courseId - ID of the course
   * @returns {Promise} - Promise with mapping response
   *
   * English: This function maps a student to a course
   * Tanglish: Indha function oru student-a oru course-oda map pannum
   */
  mapStudentToCourse: async (studentId, courseId) => {
    try {
      const response = await axios.post(`${API_URL}/map-student`, {
        student_id: studentId,
        course_id: courseId
      }, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Get all courses for a specific student
   *
   * @param {number} studentId - ID of the student
   * @returns {Promise} - Promise with student courses response
   *
   * English: This function gets all courses for a specific student
   * Tanglish: Indha function specific student-kku ella courses-um get pannum
   */
  getStudentCourses: async (studentId) => {
    try {
      const response = await axios.get(`${API_URL}/student-courses/${studentId}`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  }
};

export default courseService;
