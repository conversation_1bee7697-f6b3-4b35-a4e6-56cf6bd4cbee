/**
 * Private Route Component
 *
 * This component protects routes that require authentication.
 *
 * English: This component redirects to login if user is not authenticated
 * Tanglish: Indha component user authenticate aagala na login page-kku redirect pannum
 */

import { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import authService from '../services/authService';

const PrivateRoute = ({ children }) => {
  const { currentUser, logout } = useAuth();
  const [isVerifying, setIsVerifying] = useState(true);
  const [isValid, setIsValid] = useState(true);

  useEffect(() => {
    const verifyToken = async () => {
      if (currentUser) {
        const verification = await authService.verifyToken();
        if (!verification.valid) {
          console.warn('Token verification failed in PrivateRoute:', verification.error);
          logout();
          setIsValid(false);
        }
      }
      setIsVerifying(false);
    };

    verifyToken();
  }, [currentUser, logout]);

  // Show loading state while verifying
  if (isVerifying) {
    return <div>Verifying authentication...</div>;
  }

  // Redirect to login if not authenticated or token is invalid
  if (!currentUser || !isValid) {
    return <Navigate to="/login" />;
  }

  // Render children if authenticated and token is valid
  return children;
};

export default PrivateRoute;
