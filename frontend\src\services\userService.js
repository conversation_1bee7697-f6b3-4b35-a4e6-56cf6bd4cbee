/**
 * User Service
 *
 * This service handles user management with the User Service.
 *
 * English: This file handles user registration and management
 * Tanglish: Indha file user registration and management-a handle pannum
 */

import axios from 'axios';
import authService from './authService';

const API_URL = 'http://localhost:5001/api/users';

const userService = {
  /**
   * Register a new user
   *
   * @param {Object} userData - User data (username, password, email, role)
   * @returns {Promise} - Promise with registration response
   *
   * English: This function registers a new user
   * Tanglish: Indha function puthusa oru user-a register pannum
   */
  registerUser: async (userData) => {
    try {
      const response = await axios.post(`${API_URL}/register`, userData, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Get all users
   *
   * @returns {Promise} - Promise with users response
   *
   * English: This function gets all users
   * Tanglish: Indha function ella users-um get pannum
   */
  getUsers: async () => {
    try {
      const response = await axios.get(`${API_URL}/users`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });

      console.log('Successfully retrieved users:', response.data);
      return response.data;
    } catch (error) {
      // Check if it's a permission error (403)
      if (error.response && error.response.status === 403) {
        console.warn("Permission denied: User doesn't have access to view all users");
        console.error("Error details:", error.response.data);

        // Get the current user role from localStorage
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        console.log('Current user role from localStorage:', user.role);

        // Return empty users array instead of throwing error
        return { users: [] };
      }
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Get a specific user
   *
   * @param {number} userId - ID of the user to get
   * @returns {Promise} - Promise with user response
   *
   * English: This function gets a specific user
   * Tanglish: Indha function specific user-a get pannum
   */
  getUser: async (userId) => {
    try {
      const response = await axios.get(`${API_URL}/users/${userId}`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  }
};

export default userService;
