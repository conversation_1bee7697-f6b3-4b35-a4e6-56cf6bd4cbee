/**
 * Authentication Context
 *
 * This context provides authentication state and functions to the entire application.
 *
 * English: This file manages authentication state across the application
 * Tanglish: Indha file application-la authentication state-a manage pannum
 */

import { createContext, useState, useEffect, useContext } from 'react';
import authService from '../services/authService';

// Create the context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Load user from localStorage and verify token on initial render
  useEffect(() => {
    const loadUser = async () => {
      const user = authService.getCurrentUser();

      if (user) {
        // Verify the token is valid
        const verification = await authService.verifyToken();
        if (!verification.valid) {
          console.warn('Token verification failed:', verification.error);
          // Force logout if token is invalid
          authService.logout();
          setCurrentUser(null);
        } else {
          setCurrentUser(user);
        }
      }

      setLoading(false);
    };

    loadUser();
  }, []);

  /**
   * Login a user
   *
   * @param {string} username - User's username
   * @param {string} password - User's password
   * @returns {Promise} - Promise with login result
   *
   * English: This function logs in a user and updates the context
   * Tanglish: Indha function user-a login panni context-a update pannum
   */
  const login = async (username, password, schoolCode = null) => {
    try {
      setError('');
      const data = await authService.login(username, password, schoolCode);
      setCurrentUser(data.user);
      return data;
    } catch (error) {
      setError(error.error || 'Login failed');
      throw error;
    }
  };

  /**
   * Logout a user
   *
   * English: This function logs out a user and updates the context
   * Tanglish: Indha function user-a logout panni context-a update pannum
   */
  const logout = () => {
    authService.logout();
    setCurrentUser(null);
  };

  // Add a compatibility layer for any code that might be using schoolcode
  const compatibleCurrentUser = currentUser ? {
    ...currentUser,
    // Add schoolcode property that returns main_code for backward compatibility
    get schoolcode() {
      return this.main_code;
    }
  } : null;

  // Context value
  const value = {
    currentUser: compatibleCurrentUser,
    login,
    logout,
    error,
    isLoggedIn: !!currentUser,
    // Add helper method to get school code
    getSchoolCode: () => authService.getSchoolCode()
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
