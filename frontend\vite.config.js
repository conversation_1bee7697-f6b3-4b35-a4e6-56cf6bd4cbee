import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";
import path from "path";
import { fileURLToPath } from "url";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig(({ mode }) => {
  /* eslint-disable no-undef */
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [
      react(),
      tailwindcss()
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    define: {
      "import.meta.env.VITE_ENV": JSON.stringify(env.VITE_ENV),
      "import.meta.env.VITE_API_URL": JSON.stringify(env.VITE_API_URL),
      "import.meta.env.VITE_LOGIN_URL": JSON.stringify(env.VITE_LOGIN_URL),
      "import.meta.env.VITE_FORGET_URL": JSON.stringify(env.VITE_FORGET_URL),

      "import.meta.env.VITE_API_URL_AUTH": JSON.stringify(env.VITE_API_URL_AUTH),
      "import.meta.env.VITE_API_URL_USERS": JSON.stringify(env.VITE_API_URL_USERS),
      "import.meta.env.VITE_API_URL_STUDENTS": JSON.stringify(env.VITE_API_URL_STUDENTS),
      "import.meta.env.VITE_API_URL_COURSES": JSON.stringify(env.VITE_API_URL_COURSES),
      "import.meta.env.VITE_API_URL_PARENTS": JSON.stringify(env.VITE_API_URL_PARENTS),
      "import.meta.env.VITE_DEBUG": JSON.stringify(env.VITE_DEBUG),
      "import.meta.env.VITE_LOG_LEVEL": JSON.stringify(env.VITE_LOG_LEVEL),
    },
    server: {
      host: "0.0.0.0",
      port: 5173,
      strictPort: true,
      allowedHosts: ["localhost", "127.0.0.1"], // Add your domain here if needed
    },
  };
});
