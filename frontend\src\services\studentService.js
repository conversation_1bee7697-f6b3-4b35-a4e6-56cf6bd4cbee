/**
 * Student Service
 *
 * This service handles student management with the Student Service.
 *
 * English: This file handles student management
 * Tanglish: Indha file student management-a handle pannum
 */

import axios from 'axios';
import authService from './authService';

const API_URL = 'http://localhost:5002/api/students';

const studentService = {
  /**
   * Register a new student
   *
   * @param {Object} studentData - Student data (user_id, first_name, last_name, etc.)
   * @returns {Promise} - Promise with student registration response
   *
   * English: This function registers a new student
   * Tanglish: Indha function puthusa oru student-a register pannum
   */
  registerStudent: async (studentData) => {
    try {
      const response = await axios.post(`${API_URL}/students`, studentData, {
        headers: authService.getAuthHeader()
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Get all students
   *
   * @returns {Promise} - Promise with students response
   *
   * English: This function gets all students
   * Tanglish: Indha function ella students-um get pannum
   */
  getStudents: async () => {
    try {
      const response = await axios.get(`${API_URL}/students`, {
        headers: authService.getAuthHeader()
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Get a specific student
   *
   * @param {number} studentId - ID of the student to get
   * @returns {Promise} - Promise with student response
   *
   * English: This function gets a specific student
   * Tanglish: Indha function specific student-a get pannum
   */
  getStudent: async (studentId) => {
    try {
      const response = await axios.get(`${API_URL}/students/${studentId}`, {
        headers: authService.getAuthHeader()
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Map a parent to a student
   *
   * @param {number} parentId - ID of the parent
   * @param {number} studentId - ID of the student
   * @param {string} relationship - Relationship between parent and student
   * @returns {Promise} - Promise with mapping response
   *
   * English: This function maps a parent to a student
   * Tanglish: Indha function oru parent-a oru student-oda map pannum
   */
  mapParentToStudent: async (parentId, studentId, relationship = null) => {
    try {
      const response = await axios.post(`${API_URL}/map-parent`, {
        parent_id: parentId,
        student_id: studentId,
        relationship: relationship
      }, {
        headers: authService.getAuthHeader()
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Get all students for a specific parent
   *
   * @param {number} parentId - ID of the parent
   * @returns {Promise} - Promise with parent students response
   *
   * English: This function gets all students for a specific parent
   * Tanglish: Indha function specific parent-kku ella students-um get pannum
   */
  getParentStudents: async (parentId) => {
    try {
      const response = await axios.get(`${API_URL}/parent-students/${parentId}`, {
        headers: authService.getAuthHeader()
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  /**
   * Get the profile of the currently logged-in student
   *
   * @returns {Promise} - Promise with student profile response
   *
   * English: This function gets the profile of the currently logged-in student
   * Tanglish: Indha function login panra student-oda profile-a get pannum
   */
  getStudentProfile: async () => {
    try {
      const response = await axios.get(`${API_URL}/student-profile`, {
        headers: authService.getAuthHeader(),
        withCredentials: true
      });

      return response.data;
    } catch (error) {
      // If there's an error, fall back to using the getStudents method
      console.warn("Error getting student profile, falling back to getStudents");
      try {
        const currentUser = authService.getCurrentUser();
        if (!currentUser) {
          throw { error: 'User not authenticated' };
        }

        const data = await studentService.getStudents();
        const students = data.students || [];
        const currentStudent = students.find(s => s.user_id === parseInt(currentUser.id));

        if (!currentStudent) {
          throw { error: 'Student not found' };
        }

        return { student: currentStudent };
      } catch (fallbackError) {
        throw fallbackError.error ? fallbackError : { error: 'Failed to get student profile' };
      }
    }
  },

  /**
   * Get all parents for a specific student
   *
   * @param {number} studentId - ID of the student
   * @returns {Promise} - Promise with student parents response
   *
   * English: This function gets all parents for a specific student
   * Tanglish: Indha function specific student-kku ella parents-um get pannum
   */
  getStudentParents: async (studentId) => {
    try {
      const response = await axios.get(`${API_URL}/student-parents/${studentId}`, {
        headers: authService.getAuthHeader()
      });

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  }
};

export default studentService;
